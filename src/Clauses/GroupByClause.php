<?php

declare(strict_types=1);

namespace EsqlPhp\QueryBuilder\Clauses;

use EsqlPhp\QueryBuilder\ClauseInterface;

class GroupByClause implements ClauseInterface
{
    private array $fields = [];
    
    public function __construct(array $fields)
    {
        $this->fields = $fields;
    }
    
    public function toString(): string
    {
        if (empty($this->fields)) {
            return '';
        }
        
        return '| GROUP BY ' . implode(', ', $this->fields);
    }
}
