<?php

declare(strict_types=1);

namespace EsqlPhp\QueryBuilder\Functions\String;

use EsqlPhp\QueryBuilder\Functions\AbstractFunction;

class UpperCase extends AbstractFunction
{
    public function __construct($string)
    {
        parent::__construct('uppercase', [$string]);
    }
    
    public function toString(): string
    {
        $string = $this->arguments[0];
        $formattedString = $this->isFieldReference($string) ? $string : "'" . str_replace("'", "''", $string) . "'";
        
        return "uppercase(" . $formattedString . ")";
    }
}
