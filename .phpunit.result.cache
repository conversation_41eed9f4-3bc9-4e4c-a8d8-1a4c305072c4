{"version": 1, "defects": {"EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testAggregationQuery": 7, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testDataManipulationQuery": 7, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testAdvancedTransformationsQuery": 7, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testDocumentationExample": 7, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testStringFunctionsStatic": 7, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testDateFunctionsStatic": 7, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testAggregationFunctionsStatic": 7}, "times": {"EsqlPhp\\Tests\\Unit\\Clauses\\FromClauseTest::testFromClauseToString": 0.001, "EsqlPhp\\Tests\\Unit\\Clauses\\SortClauseTest::testToStringWithNoSorts": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\SortClauseTest::testAddSingleSortAscending": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\SortClauseTest::testAddSingleSortDescending": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\SortClauseTest::testAddMultipleSorts": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\SortClauseTest::testInvalidDirectionDefaultsToAscending": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\SortClauseTest::testCaseInsensitiveDirection": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\WhereClauseTest::testSimpleCondition": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\WhereClauseTest::testMultipleAndConditions": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\WhereClauseTest::testOrCondition": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\WhereClauseTest::testComplexConditions": 0, "EsqlPhp\\Tests\\Unit\\Clauses\\WhereClauseTest::testValueFormatting": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testSimpleQuery": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testAggregationQuery": 0.002, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testDataManipulationQuery": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testAdvancedTransformationsQuery": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testComposedConditionsQuery": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testDocumentationExample": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testMathFunctionsStatic": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testStringFunctionsStatic": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testDateFunctionsStatic": 0, "EsqlPhp\\Tests\\Unit\\QueryBuilderTest::testAggregationFunctionsStatic": 0}}