<documentation title="Nullable Type Declarations Functions">
    <standard>
    <![CDATA[
    In nullable type declarations there MUST NOT be a space between the question mark and the type.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: No whitespace used.">
        <![CDATA[
public function functionName(
    ?string $arg1,
    ?int $arg2
): ?string {
}
        ]]>
        </code>
        <code title="Invalid: Superfluous whitespace used.">
        <![CDATA[
public function functionName(
    ? string $arg1,
    ? int $arg2
): ? string {
}
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: No unexpected characters.">
        <![CDATA[
public function foo(?int $arg): ?string
{
}
        ]]>
        </code>
        <code title="Invalid: Unexpected characters used.">
        <![CDATA[
public function bar(? /* comment */ int $arg): ?
    // nullable for a reason
    string
{
}
        ]]>
        </code>
    </code_comparison>
</documentation>
