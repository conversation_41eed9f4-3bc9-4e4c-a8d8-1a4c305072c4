<documentation title="Function Declarations">
    <standard>
    <![CDATA[
    There should be exactly 1 space after the function keyword and 1 space on each side of the use keyword.  Closures should use the Kernighan/Ritchie Brace style and other single-line functions should use the BSD/Allman style.  Multi-line function declarations should have the parameter lists indented one level with the closing parenthesis on a newline followed by a single space and the opening brace of the function.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Correct spacing around function and use keywords.">
        <![CDATA[
$foo = function<em> </em>()<em> </em>use<em> </em>($bar)<em> </em>{
};
        ]]>
        </code>
        <code title="Invalid: No spacing around function and use keywords.">
        <![CDATA[
$foo = function<em></em>()<em></em>use<em></em>($bar)<em></em>{
};
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Multi-line function declaration formatted properly.">
        <![CDATA[
function foo(
<em>    </em>$bar,
<em>    </em>$baz
<em>) {</em>
};
        ]]>
        </code>
        <code title="Invalid: Invalid indentation and formatting of closing parenthesis.">
        <![CDATA[
function foo(
<em></em>$bar,
<em></em>$baz<em>)</em>
<em>{</em>
};
        ]]>
        </code>
    </code_comparison>
</documentation>
