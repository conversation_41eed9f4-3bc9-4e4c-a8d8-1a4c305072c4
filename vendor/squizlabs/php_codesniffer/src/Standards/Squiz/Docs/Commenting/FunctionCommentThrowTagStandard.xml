<documentation title="Doc Comment Throws Tag">
    <standard>
    <![CDATA[
    If a function throws any exceptions, they should be documented in a @throws tag.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: @throws tag used.">
        <![CDATA[
/**
 * <em>@throws Exception all the time</em>
 * @return void
 */
function foo()
{
    throw new Exception('Danger!');
}
        ]]>
        </code>
        <code title="Invalid: No @throws tag used for throwing function.">
        <![CDATA[
/**
 * @return void
 */
function foo()
{
    throw new Exception('Danger!');
}
        ]]>
        </code>
    </code_comparison>
</documentation>
