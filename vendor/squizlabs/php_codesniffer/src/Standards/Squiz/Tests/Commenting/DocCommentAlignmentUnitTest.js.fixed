
/**
 * Some info about the class here
 *
 */
foo.prototype = {

    /**
     * Some info about the function here.
     *
     * @return void
     */
    bar: function() {}
}

/**
 *  Some info about the class here
 *
 */
foo.prototype = {

    /**
     * Some info about the function here.
     *
     * @return void
     */
    bar: function() {}
}

/**
 * Some info about the class here
 *
 */
foo.prototype = {

    /**
     * Some info about the function here.
     *
     * @return void
     */
    bar: function() {}
}

/** @var Database $mockedDatabase */
/** @var Container $mockedContainer */

function myFunction()
{
    console.info('hi');
    /**
        Comment here.
        */
}

/**
 * Creates a map of tokens => line numbers for each token.
 *
 * Long description with some points:
 *   - one
 *   - two
 *   - three
 *
 * @param array  &$tokens   The array of tokens to process.
 * @param object $tokenizer The tokenizer being used to
 *                          process this file.
 * @param string $eolChar   The EOL character to use for splitting strings.
 *
 * @return void
 */
function myFunction() {}

$.extend(Datepicker.prototype, {
	_widgetDatepicker: function() {
	},
	/* Action for selecting a new month/year. */
});
