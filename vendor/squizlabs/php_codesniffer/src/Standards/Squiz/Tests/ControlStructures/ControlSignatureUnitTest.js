
i = 0;
do {
    i = 0;
} while (i > 0);

do
{
    i = 0;
} while (i > 0);

do
{
    i = 0;
}
while (i > 0);

do { i = 0; } while (i > 0);

do{
    i = 0;
}while(i > 0);

while (i < 1) {
    i = 0;
}

while(i < 1){
    i = 0;
}

while (i < 1) { i = 0; }

for (i = 1; i < 1; i++) {
    i = 0;
}

for(i = 1; i < 1; i++){
    i = 0;
}

for (i = 1; i < 1; i++) { i = 0; }

if (i == 0) {
    i = 1;
}

if(i == 0){
    i = 1;
}

if (i == 0) { i = 1; }

if (i == 0) {
    i = 1;
} else {
    i = 0;
}

if (i == 0) {
    i = 1;
}else{
    i = 0;
}

if (i == 0) { i = 1; } else { i = 0; }

if (i == 0) {
    i = 1;
} else if (i == 2) {
    i = 0;
}

if (i == 0) {
    i = 1;
}else   if(i == 2){
    i = 0;
}

if (i == 0) { i = 1; } else if (i == 2) { i = 0; }

if (i == 0) { // comments are allowed
    i = 1;
}

if (i == 0) {// comments are allowed
    i = 1;
}

if (i == 0) { /* comments are allowed*/
    i = 1;
}

if (i == 0)
{ // this is ok
    i = 1;
}

if (i == 0) /* this is ok */ {
}

try {
    code = 'this';
} catch (e) {
    // Caught!
}

try { code = 'this'; } catch (e) {
    // Caught!
}

do { i = 0;
} while (i > 0);

if (i === 0) {

    i = 1
}

if (window.jQuery)(function($) {
    $.fn.reset = function() {
        return this.each(function() {
            try {
                this.reset();
            } catch (e) {
            }
        });
    };
})(jQuery);

if ($("#myid").rotationDegrees()=='90')
    $('.modal').css({'transform': 'rotate(90deg)'});

if ($("#myid").rotationDegrees()=='90')
    $foo = {'transform': 'rotate(90deg)'};
