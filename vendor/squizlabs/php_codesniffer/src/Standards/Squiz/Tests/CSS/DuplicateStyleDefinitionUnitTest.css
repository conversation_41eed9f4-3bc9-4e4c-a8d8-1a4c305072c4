.ViperSubToolbar-wrapper {
    height: 34px;
    left: 0;
    position: fixed;
    top: 60px;
    z-index: 997;
    left: 50%;
}

.expandable {
    -moz-transition-property: margin-left, margin-right;
    -moz-transition-duration: 0.2s;
    -moz-transition-timing-function: ease;
    -webkit-transition-property: margin-left, margin-right;
    -webkit-transition-duration: 0.2s;
    -webkit-transition-timing-function: ease;
    z-index: 2;
}

@media only screen and (max-width: 480px) {
    header nav.meta a { display: none; }
    header nav.meta a.search { display: block; }
}

/* Live coding. Has to be the last test in the file. */
.intentional-parse-error {
	float: left
