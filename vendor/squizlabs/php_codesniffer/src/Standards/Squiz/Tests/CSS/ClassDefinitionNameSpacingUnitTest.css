.AssetListingEditWidgetType-BottomPanel select,
#EditEditingModeWidgetType-assetEditor-filters-assetTypes-addNew {
    float: left;
}

.AssetListingEditWidgetType-BottomPanel select,

#EditEditingModeWidgetType-assetEditor-filters-assetTypes-addNew {
    float: left;
}

.AssetListingEditWidgetType-BottomPanel select,
/*.AssetListingEditWidgetType-BottomPanel ul,*/
#EditEditingModeWidgetType-assetEditor-filters-assetTypes-addNew {
    float: left;
}

.AssetListingEditWidgetType-BottomPanel select,

.AssetListingEditWidgetType-BottomPanel ul,
#EditEditingModeWidgetType-assetEditor-filters-assetTypes-addNew {
    float: left;
}

#SuperUsersSystemConfigScreen-table {
    display: block;
    left: 50%;
    margin-left: -500px;
    margin-top: 180px;
    position: relative;
    width: 1000px;
}

/**
 * More styles below here.
 */

td.TableWidgetType-header.TableWidgetType-header-lastLogin,
td.TableWidgetType-header.TableWidgetType-header-remove,
td.TableWidgetType-header.TableWidgetType-header-email,
td.TableWidgetType-header.TableWidgetType-header-userName {
    background: url(images/ScreenImages/table_header_bg.png) repeat-x;
    border-top: 1px solid #D4D4D4;
    color: #787878;
    height: 33px;
    padding-left: 12px;
    width: 150px;
}

@media screen and (max-device-width: 769px) {

    header #logo img {
    max-width: 100%;
        padding: 20px;
         margin: 40px;
    }
}

.foo
{
    border: none;
}

/* Live coding. Has to be the last test in the file. */
.intentional-parse-error {
	float: left
