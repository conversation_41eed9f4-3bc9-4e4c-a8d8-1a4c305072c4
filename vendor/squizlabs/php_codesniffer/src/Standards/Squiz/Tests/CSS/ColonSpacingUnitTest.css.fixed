body {
font-family: Arial, Helvetica, sans-serif;
margin: 40px 0 0 0;
padding: 0;
background: #8FB7DB url(diag_lines_bg.gif) top left;
margin-top: 10px;
margin-bottom: 0px;
}

.TableWidgetType .recover:hover {
    background-color: #FFF;
}

#clearCache-settings:rootNodes-list_0 {
    border-top: none;
}

.LookupEditScreenWidgetType-urls a, .LookupEditScreenWidgetType-urls a:visited {
    text-decoration: none;
    color: #444;
}

/* checking embedded PHP */
li {
    background: url(<?php print $staticserver; ?>/images/<?php print $staticdir; ?>/bullet.gif) left <?php print $left; ?>px no-repeat;
    margin: 0px;
    padding-left: 10px;
    margin-bottom: <?php echo $marginBottom; ?>px;
    margin-top: <?php echo $marginTop; ?>px;
    line-height: 13px;
    filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#2e62a8, endColorstr=#123363);
}

/* Empty style defs. */
.p {
    margin:;
    margin-right:
    margin-left: 10px;
    float: /* Some comment. */ ;
}
