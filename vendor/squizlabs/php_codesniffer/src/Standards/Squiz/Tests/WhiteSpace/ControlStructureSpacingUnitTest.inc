<?php
if ($something) {
}
foreach ($this as $that) {
}
while (true) {
    for ($i = 0; $i < 10; $i++) {
    }
    if ($something) {
    }

    foreach ($this as $that) {
        do {
        } while (true);

    }
}

if ($one) {
} else ($two) {
} else if ($three) {
} elseif ($four) {
}
if ($one) {
} else ($two) {
} else if ($three) {
} elseif ($four) {
}
?>
<table>
    <tr>
        <td align="center" valign="center">
        <?php
        foreach ($this->children as $child) {
            // There should be no error after this
            // foreach, because it is followed by a
            // close PHP tag.
        }
        ?>
        </td>
    </tr>
</table>
<?php

switch ($blah) {
    case 'one':
        if ($blah) {
            // There are no spaces before break.
        }
    break;

    default:
        if ($blah) {
            // There are no spaces before break.
        }
    break;
}

switch ($blah) {
    case 'one':
        if ($blah) {
            // There are no spaces before break.
        }
    break;

    default:
        if ($blah) {
            // Code here.
        }
}

foreach ($blah as $var) {
    if ($blah) {
    }
    break;
}

while (true) {
    for ($i = 0; $i < 10; $i++) {

        if ($something) {
        }

    }

    foreach ($this as $that) {
        do {

            echo $i;
        } while (true);
    }
}

function myFunction()
{
    if ($blah) {
    }

}//end myFunction()

foreach ($this->children as $child) {
    echo $child;

}

if ($defaultPageDesign === 0
    && $defaultCascade === TRUE
    && $defaultChildDesign === 0
) {
    $settingsUpdated = FALSE;
}

foreach ( $blah as $var ) {
    if (  $blah    ) {
    }
}

if (
    $defaultPageDesign === 0
    && $defaultCascade === TRUE
    && $defaultChildDesign === 0
) {
    $settingsUpdated = FALSE;
}

$moo = 'blar';
switch ($moo)
{
    case 'blar':
        if ($moo === 'blar2') {
            $moo = 'blar'
        }
    return $moo;

    default:
        $moo = 'moo';
    break;
}

do {
}
while (true);

try {
    // Something
} catch (Exception $e) {
    // Something
}

try {

    // Something

} catch (Exception $e) {

    // Something

}

if ($one) {
}
elseif ($two) {
}
// else if something
else if ($three) {
} // else do something
else {
}

if ($one) {

}

do {
    echo 'hi';
} while (  $blah  );

if ($one) {
}
// No blank line here.
if ($two) {
}

switch ($moo)
{
    case 'blar':
        if ($moo === 'blar2') {
            $moo = 'blar'
        }

    return $moo;
}

try {
    // Something
}
catch (Exception $e) {
    // Something
}
finally {
    // Something
}

if ($foo) {


    /**
     * Comment
     */
    function foo() {
        // Code here
    }


    /**
     * Comment
     */
    class bar() {

    }//end class


}

if (true) { // some comment goes here

    echo 'foo';
}

if (true) { echo 'foo';

    echo 'foo';
}

if ($true) {
    echo 'hi 2';
}//end if
echo 'hi';

if ($true) {
    echo 'hi 2';
} // phpcs:enable Standard.Category.Sniff -- for reasons.
echo 'hi';

?>
<?php foreach($formset['Fieldset'] as $fieldset): ?>

    <?php foreach($fieldset['Field'] as $field):

        echo 'foo';
    ?>
    <?php endforeach; ?>
<?php endforeach; ?>

<?php

$expr = match( $foo ){

    1 => 1,
    2 => 2,

};
echo $expr;

if($true) {

    enum SomeEnum {}

}
