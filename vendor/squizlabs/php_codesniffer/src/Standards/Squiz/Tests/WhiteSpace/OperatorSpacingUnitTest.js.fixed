

result = 1 + 2;
result = 1 + 2;
result = 1 + 2;
result = 1 + 2;
result = 1 + 2;
result = 1 + 2;

result = 1 - 2;
result = 1 - 2;
result = 1 - 2;
result = 1 - 2;
result = 1 - 2;
result = 1 - 2;

result = 1 * 2;
result = 1 * 2;
result = 1 * 2;
result = 1 * 2;
result = 1 * 2;
result = 1 * 2;

result = 1 / 2;
result = 1 / 2;
result = 1 / 2;
result = 1 / 2;
result = 1 / 2;
result = 1 / 2;

result = 1 % 2;
result = 1 % 2;
result = 1 % 2;
result = 1 % 2;
result = 1 % 2;
result = 1 % 2;
result = '100%';

result += 4;
result += 4;
result -= 4;
result -= 4;
result /= 4;
result /= 4;
result *= 4;
result *= 4;

$.localScroll({offset: {top: -32}});

switch (result) {
	case -1:
		break;
}

result = x ? y : z;
result = x ? y : z;

if (something === true
    ^ somethingElse === true
) {
    return false;
}

y = 1 + 2 - 3;

y = 1 + 2 - 3;

y = 1 + 2 - 3;

// phpcs:set Squiz.WhiteSpace.OperatorSpacing ignoreNewlines true
y = 1
  + 2
  - 3;

y = 1 +
    2 -
    3;

y = 1
+ 2
- 3;
// phpcs:set Squiz.WhiteSpace.OperatorSpacing ignoreNewlines false

if (true || -1 == b) {
}

x = x << y;
x <<= y;
x = x >> y;
x >>= y;
x = x >>> y;
x >>>= y;

var foo = bar.map(baz => baz.length);

// phpcs:set Squiz.WhiteSpace.OperatorSpacing ignoreSpacingBeforeAssignments false
a = 3;
// phpcs:set Squiz.WhiteSpace.OperatorSpacing ignoreSpacingBeforeAssignments true
