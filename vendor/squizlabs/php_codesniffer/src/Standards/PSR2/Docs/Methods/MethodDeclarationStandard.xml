<documentation title="Method Declarations">
    <standard>
    <![CDATA[
    Method names should not be prefixed with an underscore to indicate visibility.  The static keyword, when present, should come after the visibility declaration, and the final and abstract keywords should come before.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Correct method naming.">
        <![CDATA[
class Foo
{
    private function <em>bar</em>()
    {
    }
}
        ]]>
        </code>
        <code title="Invalid: An underscore prefix used to indicate visibility.">
        <![CDATA[
class Foo
{
    private function <em>_bar</em>()
    {
    }
}
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Correct ordering of method prefixes.">
        <![CDATA[
class Foo
{
    <em>final public static</em> function <em>bar</em>()
    {
    }
}
        ]]>
        </code>
        <code title="Invalid: `static` keyword used before visibility and final used after.">
        <![CDATA[
class Foo
{
    <em>static public final</em> function <em>bar</em>()
    {
    }
}
        ]]>
        </code>
    </code_comparison>
</documentation>
