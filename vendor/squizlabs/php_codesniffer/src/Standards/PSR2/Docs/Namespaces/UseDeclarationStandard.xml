<documentation title="Namespace Declarations">
    <standard>
    <![CDATA[
    Each use declaration must contain only one namespace and must come after the first namespace declaration.  There should be one blank line after the final use statement.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: One use declaration per namespace.">
        <![CDATA[
use \Foo;
use \Bar;
        ]]>
        </code>
        <code title="Invalid: Multiple namespaces in a use declaration.">
        <![CDATA[
use <em>\Foo, \Bar</em>;
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: Use statements come after first namespace.">
        <![CDATA[
namespace \Foo;

use \Bar;
        ]]>
        </code>
        <code title="Invalid: Namespace declared after use.">
        <![CDATA[
use \Bar;

namespace \Foo;
        ]]>
        </code>
    </code_comparison>
    <code_comparison>
        <code title="Valid: A single blank line after the final use statement.">
        <![CDATA[
use \Foo;
use \Bar;
<em></em>
class Baz
{
}
        ]]>
        </code>
        <code title="Invalid: No blank line after the final use statement.">
        <![CDATA[
use \Foo;
use \Bar;
class Baz
{
}
        ]]>
        </code>
    </code_comparison>
</documentation>
