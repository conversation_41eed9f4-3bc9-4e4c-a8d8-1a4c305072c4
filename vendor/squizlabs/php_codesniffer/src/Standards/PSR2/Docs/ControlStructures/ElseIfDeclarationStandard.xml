<documentation title="Elseif Declarations">
    <standard>
    <![CDATA[
    PHP's elseif keyword should be used instead of else if.
    ]]>
    </standard>
    <code_comparison>
        <code title="Valid: Single word elseif keyword used.">
        <![CDATA[
if ($foo) {
    $var = 1;
} <em>elseif</em> ($bar) {
    $var = 2;
}
        ]]>
        </code>
        <code title="Invalid: Separate else and if keywords used.">
        <![CDATA[
if ($foo) {
    $var = 1;
} <em>else if</em> ($bar) {
    $var = 2;
}
        ]]>
        </code>
    </code_comparison>
</documentation>
