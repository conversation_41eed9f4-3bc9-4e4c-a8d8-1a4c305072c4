<?php
/**
 * An exception thrown by PHP_CodeSniffer when it encounters an unrecoverable tokenizer error.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2006-2015 Squiz Pty Ltd (ABN **************)
 * @license   https://github.com/PHPCSStandards/PHP_CodeSniffer/blob/master/licence.txt BSD Licence
 */

namespace PHP_CodeSniffer\Exceptions;

use Exception;

class TokenizerException extends Exception
{

}//end class
