<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="SetSniffPropertyTest" xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/PHPCSStandards/PHP_CodeSniffer/master/phpcs.xsd">

    <rule ref="./tests/Core/Ruleset/Fixtures/TestStandard/Sniffs/SetProperty/NotAllowedViaAttributeSniff.php">
        <properties>
            <property name="arbitrarystring" value="arbitraryvalue"/>
        </properties>
    </rule>

</ruleset>
