# GeneratorTest Coding Standard

## Standard Element, line wrapping handling

This line has to be exactly 99 chars to test part of the logic.------------------------------------  
And this line has to be exactly 100 chars.----------------------------------------------------------  
And here we have a line which should start wrapping as it is longer than 100 chars. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean pellentesque iaculis enim quis hendrerit. Morbi ultrices in odio pharetra commodo.

Documentation generated on *REDACTED* by [PHP_CodeSniffer *VERSION*](https://github.com/PHPCSStandards/PHP_CodeSniffer)
