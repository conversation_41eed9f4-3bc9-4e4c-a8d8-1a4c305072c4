
------------------------------------------------------------
| GENERATORTEST CODING STANDARD: CODE TITLE, LINE WRAPPING |
------------------------------------------------------------

This is a standard block.

----------------------------------------- CODE COMPARISON ------------------------------------------
| Valid: exactly 45 character long description.  | Invalid: exactly 45 char long description---.   |
----------------------------------------------------------------------------------------------------
| // Dummy.                                      | // Dummy.                                       |
----------------------------------------------------------------------------------------------------

----------------------------------------- CODE COMPARISON ------------------------------------------
| Valid: exactly 46 character long description-. | Invalid: exactly 46 character long description  |
----------------------------------------------------------------------------------------------------
| // Dummy.                                      | // Dummy.                                       |
----------------------------------------------------------------------------------------------------

----------------------------------------- CODE COMPARISON ------------------------------------------
| Valid: exactly 47 character long               | Invalid: exactly 47 character long              |
| description--.                                 | description.                                    |
----------------------------------------------------------------------------------------------------
| // Dummy.                                      | // Dummy.                                       |
----------------------------------------------------------------------------------------------------

----------------------------------------- CODE COMPARISON ------------------------------------------
| Valid: this description is longer than 46      | Invalid: this description is longer than 46     |
| characters and will wrap.                      | characters and will wrap.                       |
----------------------------------------------------------------------------------------------------
| // Dummy.                                      | // Dummy.                                       |
----------------------------------------------------------------------------------------------------

