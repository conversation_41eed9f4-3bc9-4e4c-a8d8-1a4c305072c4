<?php declare(strict_types=1);
/*
 * This file is part of phpunit/php-code-coverage.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\CodeCoverage\Driver;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\CodeCoverage\Filter;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\CodeCoverage\NoCodeCoverageDriverAvailableException;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\CodeCoverage\NoCodeCoverageDriverWithPathCoverageSupportAvailableException;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Environment\Runtime;

final class Selector
{
    /**
     * @throws NoCodeCoverageDriverAvailableException
     * @throws PcovNotAvailableException
     * @throws XdebugNotAvailableException
     * @throws XdebugNotEnabledException
     */
    public function forLineCoverage(Filter $filter): Driver
    {
        $runtime = new Runtime;

        if ($runtime->hasPCOV()) {
            return new PcovDriver($filter);
        }

        if ($runtime->hasXdebug()) {
            $driver = new XdebugDriver($filter);

            $driver->enableDeadCodeDetection();

            return $driver;
        }

        throw new NoCodeCoverageDriverAvailableException;
    }

    /**
     * @throws NoCodeCoverageDriverWithPathCoverageSupportAvailableException
     * @throws XdebugNotAvailableException
     * @throws XdebugNotEnabledException
     */
    public function forLineAndPathCoverage(Filter $filter): Driver
    {
        if ((new Runtime)->hasXdebug()) {
            $driver = new XdebugDriver($filter);

            $driver->enableDeadCodeDetection();
            $driver->enableBranchAndPathCoverage();

            return $driver;
        }

        throw new NoCodeCoverageDriverWithPathCoverageSupportAvailableException;
    }
}
